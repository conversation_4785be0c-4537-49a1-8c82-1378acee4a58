import 'dart:ui' show ImageFilter;

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

import 'online_status_indicator.dart';
import 'profile_picture_widget.dart';

/// A card widget to display summarized user profile information.
///
/// Includes profile picture, name, username, and online visibility.
/// Styles itself similarly to BubbleStatusCard.
class UserProfileCard extends StatelessWidget {
  const UserProfileCard({
    required this.firstName,
    required this.lastName,
    required this.username,
    required this.isOnlineVisible,
    required this.baseTileHeight,
    super.key,
    this.avatarUrl,
    this.onProfilePictureTap,
  });
  final String firstName;
  final String lastName;
  final String username;
  final String? avatarUrl; // Can be null
  final bool isOnlineVisible;
  final double baseTileHeight;
  final VoidCallback? onProfilePictureTap;

  @override
  Widget build(BuildContext context) {
    print('🖼️ UserProfileCard.build() - avatarUrl: $avatarUrl, firstName: $firstName, lastName: $lastName');
    
    final targetHeight = baseTileHeight * 1.6; // Match BubbleStatusCard height
    final avatarSize =
        targetHeight * 0.7; // Match ProfileOptionTile avatar proportion
    final verticalPadding = MediaQuery.of(context).size.height / 96;
    final nameFontSize = baseTileHeight * 0.25;
    final usernameFontSize = baseTileHeight * 0.18;
    final onlineIndicatorSize =
        nameFontSize * 0.5; // Adjusted size for indicator

    return Padding(
      padding: EdgeInsets.only(bottom: verticalPadding),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Container(
            height: targetHeight,
            decoration: ShapeDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFF00C4FF), Color(0xFF00A3E0)],
              ),
              shape: RoundedSuperellipseBorder(
                borderRadius: BorderRadius.circular(36),
                side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: onProfilePictureTap,
                  child: ProfilePictureWidget(
                    imageUrl: avatarUrl,
                    firstName: firstName,
                    lastName: lastName,
                    radius: avatarSize / 2,
                    backgroundColor: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          AutoSizeText(
                            '$firstName $lastName',
                            style: TextStyle(
                              fontSize: nameFontSize,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (isOnlineVisible)
                            Padding(
                              padding: const EdgeInsets.only(left: 8),
                              child: OnlineStatusIndicator(
                                isOnline: true, // Assuming true if visible
                                size: onlineIndicatorSize,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      AutoSizeText(
                        '@$username',
                        style: TextStyle(
                          fontSize: usernameFontSize,
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                        maxLines: 1,
                        minFontSize: 10,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper to resolve image (copied from FriendsTile)
  ImageProvider _resolveImage(String url) {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return NetworkImage(url);
    } else {
      return AssetImage(url);
    }
  }
}
