import 'dart:ui' show ImageFilter;

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../../statefulbusinesslogic/core/services/online_status_service.dart';
import 'online_status_indicator.dart';
import 'profile_picture_widget.dart';

/// A card widget to display summarized user profile information.
///
/// Includes profile picture, name, username, and dynamic online visibility.
/// Styles itself similarly to BubbleStatusCard.
///
/// The online status is computed dynamically based on:
/// - App lifecycle state (foreground/background)
/// - User privacy preferences
/// - Backend online status
class UserProfileCard extends StatefulWidget {
  const UserProfileCard({
    required this.firstName,
    required this.lastName,
    required this.username,
    required this.baseTileHeight,
    super.key,
    this.avatarUrl,
    this.onProfilePictureTap,
    this.userOnlineStatus = OnlineStatus.offline,
    this.userContactIds = const [],
    this.userFriendIds = const [],
    this.viewerUserId,
    this.isCurrentUser = false,
  });

  final String firstName;
  final String lastName;
  final String username;
  final String? avatarUrl; // Can be null
  final double baseTileHeight;
  final VoidCallback? onProfilePictureTap;
  final OnlineStatus userOnlineStatus; // Backend online status
  final List<String> userContactIds; // For privacy visibility checks
  final List<String> userFriendIds; // For privacy visibility checks
  final String? viewerUserId; // Who is viewing this profile
  final bool isCurrentUser; // Is this the current user's own profile

  @override
  State<UserProfileCard> createState() => _UserProfileCardState();
}

class _UserProfileCardState extends State<UserProfileCard> {
  bool _isOnlineVisible = false;
  late OnlineStatusService _onlineStatusService;

  @override
  void initState() {
    super.initState();
    _onlineStatusService = OnlineStatusService.instance;

    // Initialize the service if not already done
    _onlineStatusService.initialize();

    // Update backend status
    _onlineStatusService.updateBackendOnlineStatus(widget.userOnlineStatus);

    // Listen to visibility changes
    _onlineStatusService.visibilityStream.listen((isVisible) {
      if (mounted) {
        setState(() {
          _isOnlineVisible = _computeVisibilityForViewer(isVisible);
        });
      }
    });

    // Set initial visibility
    _isOnlineVisible = _computeVisibilityForViewer(_onlineStatusService.isOnlineVisible);
  }

  @override
  void didUpdateWidget(UserProfileCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update backend status if it changed
    if (oldWidget.userOnlineStatus != widget.userOnlineStatus) {
      _onlineStatusService.updateBackendOnlineStatus(widget.userOnlineStatus);
    }

    // Recompute visibility
    _isOnlineVisible = _computeVisibilityForViewer(_onlineStatusService.isOnlineVisible);
  }

  /// Compute visibility based on privacy settings and viewer relationship
  bool _computeVisibilityForViewer(bool baseVisibility) {
    if (!baseVisibility) return false;

    // If this is the current user's own profile, show their actual status
    if (widget.isCurrentUser) {
      return baseVisibility;
    }

    // For other users viewing this profile, check privacy settings
    return _onlineStatusService.getVisibilityForViewer(
      widget.viewerUserId,
      widget.userContactIds,
      widget.userFriendIds,
    );
  }

  @override
  Widget build(BuildContext context) {
    print('🖼️ UserProfileCard.build() - avatarUrl: ${widget.avatarUrl}, firstName: ${widget.firstName}, lastName: ${widget.lastName}, isOnlineVisible: $_isOnlineVisible');

    final targetHeight = widget.baseTileHeight * 1.6; // Match BubbleStatusCard height
    final avatarSize =
        targetHeight * 0.7; // Match ProfileOptionTile avatar proportion
    final verticalPadding = MediaQuery.of(context).size.height / 96;
    final nameFontSize = widget.baseTileHeight * 0.25;
    final usernameFontSize = widget.baseTileHeight * 0.18;
    final onlineIndicatorSize =
        nameFontSize * 0.5; // Adjusted size for indicator

    return Padding(
      padding: EdgeInsets.only(bottom: verticalPadding),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Container(
            height: targetHeight,
            decoration: ShapeDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFF00C4FF), Color(0xFF00A3E0)],
              ),
              shape: RoundedSuperellipseBorder(
                borderRadius: BorderRadius.circular(36),
                side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: widget.onProfilePictureTap,
                  child: ProfilePictureWidget(
                    imageUrl: widget.avatarUrl,
                    firstName: widget.firstName,
                    lastName: widget.lastName,
                    radius: avatarSize / 2,
                    backgroundColor: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          AutoSizeText(
                            '${widget.firstName} ${widget.lastName}',
                            style: TextStyle(
                              fontSize: nameFontSize,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (_isOnlineVisible)
                            Padding(
                              padding: const EdgeInsets.only(left: 8),
                              child: OnlineStatusIndicator(
                                isOnline: true, // Show indicator only when visible
                                size: onlineIndicatorSize,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      AutoSizeText(
                        '@${widget.username}',
                        style: TextStyle(
                          fontSize: usernameFontSize,
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                        maxLines: 1,
                        minFontSize: 10,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper to resolve image (copied from FriendsTile)
  ImageProvider _resolveImage(String url) {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return NetworkImage(url);
    } else {
      return AssetImage(url);
    }
  }
}
