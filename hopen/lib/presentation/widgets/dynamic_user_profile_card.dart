import 'package:flutter/material.dart';
import '../../statefulbusinesslogic/core/services/activity_status_service.dart';
import 'user_profile_card.dart';

/// Wrapper widget that provides dynamic online status to UserProfileCard
/// 
/// This widget listens to the ActivityStatusService and automatically updates
/// the online status based on app lifecycle and user preferences.
/// 
/// This follows the four-layer dependency rule by being in the presentation layer
/// and only depending on the business logic layer (ActivityStatusService).
class DynamicUserProfileCard extends StatefulWidget {
  const DynamicUserProfileCard({
    required this.firstName,
    required this.lastName,
    required this.username,
    required this.baseTileHeight,
    super.key,
    this.avatarUrl,
    this.onProfilePictureTap,
  });

  final String firstName;
  final String lastName;
  final String username;
  final String? avatarUrl;
  final double baseTileHeight;
  final VoidCallback? onProfilePictureTap;

  @override
  State<DynamicUserProfileCard> createState() => _DynamicUserProfileCardState();
}

class _DynamicUserProfileCardState extends State<DynamicUserProfileCard> {
  late ActivityStatusService _activityStatusService;
  bool _isOnlineVisible = true; // Default to true (show activity by default)

  @override
  void initState() {
    super.initState();
    _activityStatusService = ActivityStatusService.instance;
    
    // Initialize the service if not already done
    _activityStatusService.initialize();
    
    // Listen to visibility changes
    _activityStatusService.visibilityStream.listen((isVisible) {
      if (mounted) {
        setState(() {
          _isOnlineVisible = isVisible;
        });
      }
    });
    
    // Set initial visibility
    _isOnlineVisible = _activityStatusService.isOnlineVisible;
  }

  @override
  Widget build(BuildContext context) {
    return UserProfileCard(
      firstName: widget.firstName,
      lastName: widget.lastName,
      username: widget.username,
      avatarUrl: widget.avatarUrl,
      isOnlineVisible: _isOnlineVisible,
      baseTileHeight: widget.baseTileHeight,
      onProfilePictureTap: widget.onProfilePictureTap,
    );
  }
}
