import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../../statefulbusinesslogic/bloc/contacts/contacts_bloc.dart';
import '../../../statefulbusinesslogic/bloc/contacts/contacts_event.dart';
import '../../../statefulbusinesslogic/bloc/contacts/contacts_state.dart';
import '../../../statefulbusinesslogic/core/models/bubble_membership_status.dart';
import '../../../statefulbusinesslogic/core/models/contact.dart';
import '../../../statefulbusinesslogic/core/models/relationship_type.dart';
import '../../../statefulbusinesslogic/core/notifiers/nav_bar_visibility_notifier.dart';
import '../../router/app_router.dart';
import '../../widgets/bubble_status_badge.dart';
import '../../widgets/contact_request_sent_badge.dart';
import '../../widgets/custom_alert_dialog.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/main_app_bar.dart';
import '../../widgets/online_status_indicator.dart';
import '../../widgets/profile_picture_widget.dart';
import '../unified_profile_page/unified_profile_page.dart';

/// A stateful widget that displays and manages the user's contacts and potential connections.
///
/// The ContactsPage implements a comprehensive contact management interface with the following features:
/// * Search functionality for filtering contacts and users
/// * Contact list with visual status indicators
/// * Bubble status management
/// * Contact request handling
///
/// The page uses a combination of [StatefulWidget] for managing the search state and
/// custom widgets for rendering individual contact items.
///
/// Key components:
/// * Search bar with real-time filtering
/// * Contact list with avatar display
/// * Status indicators for bubble membership
/// * Action buttons for contact management
class ContactsPage extends StatefulWidget {
  const ContactsPage({super.key, this.showInvitePopup = false});
  final bool showInvitePopup;

  @override
  State<ContactsPage> createState() => _ContactsPageState();
}

/// The state class for [ContactsPage] that manages the search functionality
/// and contact list rendering.
///
/// This class maintains:
/// * Search text controller
/// * Contact list data (currently using sample data)
/// * UI state and layout
class _ContactsPageState extends State<ContactsPage> {
  /// Controller for the search input field
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  BubbleMembershipStatus? _selectedBubbleStatus;
  RelationshipType? _selectedRelationshipFilter;
  final bool _currentUserIsInBubble = true; // Default value

  // Debug flag for showing filter badge hitboxes - set to true to enable debug mode
  static const bool _showDebugHitboxes = false;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      final query = _searchController.text.trim();
      print('🔍 ContactsPage: Search query changed to: "$query"');
      setState(() {
        _searchQuery = query.toLowerCase();
      });

      // Only dispatch search event if query is not empty
      // When empty, behave as if search functionality hasn't been activated
      if (query.isNotEmpty) {
        print('🔍 ContactsPage: Dispatching SearchContacts event with query: "$query"');
        context.read<ContactsBloc>().add(SearchContacts(query));
      } else {
        print('🔍 ContactsPage: Search cleared, loading normal contacts');
        context.read<ContactsBloc>().add(LoadContacts());
      }
    });

    // Load contacts via BLoC
    Future.microtask(() => context.read<ContactsBloc>().add(LoadContacts()));

    // Show popup after a short delay if requested
    if (widget.showInvitePopup) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog<void>(
          context: context,
          builder:
              (context) => const CustomAlertDialog(
                title: 'Find contacts',
                content: 'Find contact(s) to invite to your bubble!',
              ),
        );
      });
    }
  }

  List<UserContact> _filterContacts(List<UserContact> contacts) {
    var filteredList = List<UserContact>.from(
      contacts.where((contact) {
        // Skip bubblers (they should not appear on contacts page)
        if (contact.relationshipType == RelationshipType.bubbler) {
          return false;
        }

        // Skip friends (they should only appear on friends page)
        if (contact.relationshipType == RelationshipType.friend) {
          return false;
        }

        // Apply text search (match against name and username)
        if (_searchQuery.isNotEmpty) {
          final lowerQuery = _searchQuery;
          final nameMatch = contact.name.toLowerCase().contains(lowerQuery);
          final usernameMatch = contact.username?.toLowerCase().contains(lowerQuery) ?? false;
          if (!nameMatch && !usernameMatch) {
            return false;
          }
        }

        // Apply bubble status filter
        if (_selectedBubbleStatus != null &&
            contact.bubbleStatus != _selectedBubbleStatus) {
          return false;
        }

        // Apply relationship filter
        if (_selectedRelationshipFilter != null &&
            contact.relationshipType != _selectedRelationshipFilter) {
          return false;
        }

        return true;
      }),
    );

    // Sort by relationship closeness (closest relationships first)
    filteredList.sort((a, b) {
      // Define closeness priority order (lower number = closer relationship)
      int getClosenessRank(RelationshipType type) {
        switch (type) {
          case RelationshipType.contact:
            return 1; // Closest (on this page)
          case RelationshipType.contactRequestSent:
            return 2; // Pending relationship
          case RelationshipType.contactRequestReceived:
            return 3; // Pending relationship
          case RelationshipType.none:
            return 4; // No relationship
          default:
            return 5; // Unknown/other
        }
      }

      final aCloseness = getClosenessRank(a.relationshipType);
      final bCloseness = getClosenessRank(b.relationshipType);

      if (aCloseness != bCloseness) {
        return aCloseness.compareTo(bCloseness);
      }

      // If both have the same closeness rank, sort alphabetically by name
      return a.name.compareTo(b.name);
    });

    return filteredList;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Widget _buildFilterChips() {
    final screenHeight = MediaQuery.of(context).size.height;

    final chipFontSize = (screenHeight * 0.010).clamp(7.0, 9.0);
    final chipVerticalPadding = (screenHeight * 0.0025).clamp(1.5, 2.5);
    final chipHorizontalPadding = (MediaQuery.of(context).size.width * 0.010)
        .clamp(4.0, 6.0);

    return Center(
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Bubble Status Filters
              _buildFilterStatusChip(
                BubbleMembershipStatus.noBubble,
                chipFontSize,
                chipVerticalPadding,
                chipHorizontalPadding,
                _selectedBubbleStatus == BubbleMembershipStatus.noBubble,
                (selected) {
                  setState(() {
                    _selectedBubbleStatus =
                        selected ? BubbleMembershipStatus.noBubble : null;
                  });
                },
              ),
              const SizedBox(width: 8),
              _buildFilterStatusChip(
                BubbleMembershipStatus.notFullBubble,
                chipFontSize,
                chipVerticalPadding,
                chipHorizontalPadding,
                _selectedBubbleStatus == BubbleMembershipStatus.notFullBubble,
                (selected) {
                  setState(() {
                    _selectedBubbleStatus =
                        selected ? BubbleMembershipStatus.notFullBubble : null;
                  });
                },
              ),
              const SizedBox(width: 8),
              _buildFilterStatusChip(
                BubbleMembershipStatus.fullBubble,
                chipFontSize,
                chipVerticalPadding,
                chipHorizontalPadding,
                _selectedBubbleStatus == BubbleMembershipStatus.fullBubble,
                (selected) {
                  setState(() {
                    _selectedBubbleStatus =
                        selected ? BubbleMembershipStatus.fullBubble : null;
                  });
                },
              ),
              const SizedBox(width: 8),

              // Relationship Filters
              _buildRelationshipFilterChip(
                'no relation',
                Colors.blue,
                Colors.blue,
                chipFontSize,
                chipVerticalPadding,
                chipHorizontalPadding,
                _selectedRelationshipFilter == RelationshipType.none,
                (selected) {
                  setState(() {
                    _selectedRelationshipFilter =
                        selected ? RelationshipType.none : null;
                  });
                },
              ),
              const SizedBox(width: 8),
              _buildRelationshipFilterChip(
                'contact',
                const Color(0xFF00FFFF),
                const Color(0xFF00FFFF),
                chipFontSize,
                chipVerticalPadding,
                chipHorizontalPadding,
                _selectedRelationshipFilter == RelationshipType.contact,
                (selected) {
                  setState(() {
                    _selectedRelationshipFilter =
                        selected ? RelationshipType.contact : null;
                  });
                },
              ),
              const SizedBox(width: 8),

              // "No Filters" Chip - (Previously "Clear Filters")
              Builder(
                builder: (context) {
                  Widget chip = GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedBubbleStatus = null;
                        _selectedRelationshipFilter = null;
                        _searchController.clear();
                        _searchQuery = '';
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal:
                            chipHorizontalPadding *
                            1.5, // Keep slightly wider padding for this distinct button
                        vertical: chipVerticalPadding,
                      ),
                      decoration: ShapeDecoration(
                        color: Colors.white.withOpacity(
                          0.1,
                        ), // Match Contact badge background
                        shape: RoundedSuperellipseBorder(
                          borderRadius: BorderRadius.circular(5),
                          side: BorderSide(
                            color: Colors.white.withOpacity(
                              0.3,
                            ), // Match Contact badge border
                          ),
                        ),
                      ),
                      child: Text(
                        'no filters', // Renamed from "clear filters"
                        style: TextStyle(
                          fontSize: chipFontSize,
                          color: Colors.white, // Match Contact badge text color
                          height: 1,
                        ),
                      ),
                    ),
                  );

                  // Add debug hitbox if enabled - larger but non-overlapping
                  if (_showDebugHitboxes) {
                    chip = Container(
                      margin: const EdgeInsets.all(2), // Add margin to prevent overlap
                      padding: const EdgeInsets.all(4), // Add padding to increase hitbox
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.red, width: 2),
                      ),
                      child: chip,
                    );
                  }

                  return chip;
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper for building bubble status filter chips that match the design of the badges
  Widget _buildFilterStatusChip(
    BubbleMembershipStatus status,
    double fontSize,
    double verticalPadding,
    double horizontalPadding,
    bool isSelected,
    void Function(bool) onSelected,
  ) {
    late final Color backgroundColor;
    late final String label;
    late final Color shadowColor;

    switch (status) {
      case BubbleMembershipStatus.noBubble:
        backgroundColor = const Color(0xFF67E8F9);
        shadowColor = const Color(0xFF67E8F9);
        label = 'no bubble';
      case BubbleMembershipStatus.notFullBubble:
        backgroundColor = const Color(0xFF4A90E2);
        shadowColor = const Color(0xFF4A90E2);
        label = 'in a bubble';
      case BubbleMembershipStatus.fullBubble:
        backgroundColor = const Color(0xFF7B8FA1);
        shadowColor = const Color(0xFF7B8FA1);
        label = 'bubble full';
    }

    Widget chip = GestureDetector(
      onTap: () => onSelected(!isSelected),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: verticalPadding,
        ),
        decoration: ShapeDecoration(
          color:
              isSelected
                  ? backgroundColor
                  : backgroundColor.withValues(alpha: 0.5),
          shape: RoundedSuperellipseBorder(
            borderRadius: BorderRadius.circular(5),
          ),
          shadows:
              isSelected
                  ? [
                    BoxShadow(
                      offset: const Offset(0, 2),
                      blurRadius: 8,
                      spreadRadius: 0.5,
                      color: shadowColor.withValues(alpha: 0.5),
                    ),
                  ]
                  : null,
        ),
        child: Text(
          label,
          style: TextStyle(fontSize: fontSize, color: Colors.white, height: 1),
        ),
      ),
    );

    // Add debug hitbox if enabled - larger but non-overlapping
    if (_showDebugHitboxes) {
      chip = Container(
        margin: const EdgeInsets.all(2), // Add margin to prevent overlap
        padding: const EdgeInsets.all(4), // Add padding to increase hitbox
        decoration: BoxDecoration(
          border: Border.all(color: Colors.red, width: 2),
        ),
        child: chip,
      );
    }

    return chip;
  }

  // Helper for building relationship filter chips
  Widget _buildRelationshipFilterChip(
    String label,
    Color backgroundColor,
    Color shadowColor,
    double fontSize,
    double verticalPadding,
    double horizontalPadding,
    bool isSelected,
    void Function(bool) onSelected,
  ) {
    Widget chip = GestureDetector(
      onTap: () => onSelected(!isSelected),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: verticalPadding,
        ),
        decoration: ShapeDecoration(
          color:
              isSelected
                  ? backgroundColor
                  : backgroundColor.withValues(alpha: 0.5),
          shape: RoundedSuperellipseBorder(
            borderRadius: BorderRadius.circular(5),
          ),
          shadows:
              isSelected
                  ? [
                    BoxShadow(
                      offset: const Offset(0, 2),
                      blurRadius: 8,
                      spreadRadius: 0.5,
                      color: shadowColor.withValues(alpha: 0.5),
                    ),
                  ]
                  : null,
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: fontSize,
            color:
                isSelected && backgroundColor == const Color(0xFF00FFFF)
                    ? Colors.black54
                    : Colors.white,
            height: 1,
          ),
        ),
      ),
    );

    // Add debug hitbox if enabled - larger but non-overlapping
    if (_showDebugHitboxes) {
      chip = Container(
        margin: const EdgeInsets.all(2), // Add margin to prevent overlap
        padding: const EdgeInsets.all(4), // Add padding to increase hitbox
        decoration: BoxDecoration(
          border: Border.all(color: Colors.red, width: 2),
        ),
        child: chip,
      );
    }

    return chip;
  }

  @override
  Widget build(BuildContext context) => GradientBackground(
    child: Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          // Scrollable content layer (behind header)
          Padding(
            padding: const EdgeInsets.only(top: 140), // Space for fixed header + search
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
            _buildFilterChips(),
            const SizedBox(height: 8),
            Expanded(
              child: BlocBuilder<ContactsBloc, ContactsState>(
                builder: (context, state) {
                  if (state.status == ContactsStatus.loading) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (state.status == ContactsStatus.error) {
                    return Center(
                      child: Text(
                        'Error: ${state.errorMessage}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    );
                  } else if (state.status == ContactsStatus.loaded) {
                    final filteredContacts = _filterContacts(state.contacts);

                    if (filteredContacts.isEmpty) {
                      return Center(
                        child: Text(
                          _searchQuery.isNotEmpty ||
                                  _selectedBubbleStatus != null ||
                                  _selectedRelationshipFilter != null
                              ? 'No users match your search or filters.'
                              : 'No users available.',
                          style: const TextStyle(color: Colors.grey),
                          textAlign: TextAlign.center,
                        ),
                      );
                    }

                    return ListView.builder(
                      padding: const EdgeInsets.only(
                        left: 20,
                        right: 20,
                        bottom: kBottomNavigationBarHeight + 20.0,
                      ),
                      itemCount: filteredContacts.length,
                      itemBuilder: (context, index) {
                        final contact = filteredContacts[index];
                        return GestureDetector(
                          onTap: () async {
                            final navBarNotifier =
                                Provider.of<NavBarVisibilityNotifier>(
                                  context,
                                  listen: false,
                                );
                            navBarNotifier.hideNavBar();
                            await context.push(
                              '${AppRoutes.userProfile}/${contact.id}',
                              extra: contact,
                            );
                            if (context.mounted) {
                              navBarNotifier.showNavBar();
                            }
                          },
                          child: ContactListTile(
                            contact: contact,
                            currentUserIsInBubble: _currentUserIsInBubble,
                          ),
                        );
                      },
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
              ],
            ),
          ),
          // Fixed header overlay with gradient
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 140, // Height to cover app bar + title + search
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.9), // Top: 90% opacity
                    Colors.black.withValues(alpha: 0.0), // Bottom: 0% opacity
                  ],
                ),
              ),
              child: SafeArea(
                bottom: false,
                child: Column(
                  children: [
                    const MainAppBar(),
                    const SizedBox(height: 8),
                    const Center(
                      child: Text(
                        'Contacts',
                        style: TextStyle(
                          color: Color(0xFF00FFFF),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: CustomTextField(
                        controller: _searchController,
                        hintText: 'Search contacts and users',
                        prefixIcon: Icon(
                          Icons.search,
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    ),
  );

  void _showContactProfile(String contactId) {
    final navBarNotifier = Provider.of<NavBarVisibilityNotifier>(context, listen: false);
    navBarNotifier.hideNavBar();
    showDialog(
      context: context,
      builder: (context) => UnifiedProfilePage(userId: contactId),
    ).then((_) => navBarNotifier.showNavBar());
  }
}

/// A custom list tile widget for displaying individual contacts with their status
/// and available actions.
///
/// Features:
/// * Contact avatar display
/// * Name and bubble status presentation
/// * Contextual action buttons based on contact status
/// * Visual status indicators
///
/// The widget adapts its appearance and available actions based on the contact's
/// current status (bubble membership, contact status, etc.).
class ContactListTile extends StatelessWidget {
  /// Creates a ContactListTile widget.
  ///
  /// Requires a [UserContact] instance containing all necessary contact information.
  const ContactListTile({
    required this.contact,
    required this.currentUserIsInBubble,
    super.key,
  });

  /// The contact data to display in this tile
  final UserContact contact;
  final bool currentUserIsInBubble;

  // Add helper for responsive name size
  double _getContactNameSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    // Smaller responsive range than before
    return width < 360
        ? 14.0
        : width < 600
        ? 15.0
        : 16.0;
  }

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions for responsive sizing
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    // Define responsive sizes
    final tileHeight = screenHeight / 12;
    final avatarSize = tileHeight * 0.7;
    final avatarRadius = avatarSize / 2;
    final horizontalSpacing = (screenWidth * 0.03).clamp(10.0, 16.0);
    final verticalPadding = (screenHeight * 0.008).clamp(6.0, 8.0);
    final chipVerticalPadding = (screenHeight * 0.004).clamp(3.0, 4.0);
    final chipHorizontalPadding = (screenWidth * 0.015).clamp(6.0, 8.0);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: verticalPadding),
      child: Row(
        children: [
          ProfilePictureWidget(
            imageUrl: contact.imageUrl,
            firstName: contact.name.split(' ').first,
            lastName: contact.name.split(' ').length > 1 ? contact.name.split(' ').last : '',
            radius: avatarRadius,
            backgroundColor: Colors.white.withValues(alpha: 0.1),
          ),
          SizedBox(width: horizontalSpacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  children: [
                    Flexible(
                      child: Text(
                        contact.name,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: _getContactNameSize(
                            context,
                          ), // Use responsive helper
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (contact.isOnline) // Conditionally show indicator
                      Padding(
                        padding: const EdgeInsets.only(left: 6),
                        child: OnlineStatusIndicator(
                          isOnline: contact.isOnline,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                BubbleStatusBadge(status: contact.bubbleStatus),
              ],
            ),
          ),
          SizedBox(width: horizontalSpacing / 2),
          // Show appropriate badge based on relationship type
          if (contact.relationshipType == RelationshipType.contact)
            _buildContactBadge(chipVerticalPadding, chipHorizontalPadding)
          else if (contact.relationshipType == RelationshipType.contactRequestSent)
            const ContactRequestSentBadge()
          else if (contact.relationshipType == RelationshipType.contactRequestReceived)
            _buildContactRequestReceivedBadge(chipVerticalPadding, chipHorizontalPadding)
          else
            const SizedBox.shrink(),
        ],
      ),
    );
  }

  /// Builds a "Contact" badge for users with a contact relationship
  Widget _buildContactBadge(double verticalPadding, double horizontalPadding) =>
      Container(
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: verticalPadding * 1.5,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white.withValues(alpha: 0.1),
          border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
        ),
        child: const Text(
          'Contact',
          style: TextStyle(
            fontSize: 11, // Use small fixed size for badge text
            color: Colors.white,
            height: 1,
          ),
        ),
      );

  /// Builds a "Request Received" badge for users who sent a contact request
  Widget _buildContactRequestReceivedBadge(double verticalPadding, double horizontalPadding) =>
      Container(
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: verticalPadding * 1.5,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: const Color(0xFFFF9800).withValues(alpha: 0.2),
          border: Border.all(color: const Color(0xFFFF9800).withValues(alpha: 0.5)),
        ),
        child: const Text(
          'Request',
          style: TextStyle(
            fontSize: 11,
            color: Color(0xFFFF9800),
            height: 1,
          ),
        ),
      );
}
