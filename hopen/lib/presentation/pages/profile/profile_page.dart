import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';


import '../../../statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_event.dart';
import '../../../statefulbusinesslogic/core/models/user_model.dart';
import '../../../statefulbusinesslogic/core/notifiers/nav_bar_visibility_notifier.dart';
import '../../router/app_router.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/main_app_bar.dart';
import '../../widgets/profile_option_tile.dart';
import '../../widgets/profile_picture_widget.dart';
import '../../widgets/dynamic_user_profile_card.dart';
import './about_page.dart';
import 'edit_profile_page.dart';
import 'help_support_page.dart';
import 'language_selection_page.dart';
import 'notifications_page.dart';
import 'privacy_page.dart';
import 'security_page.dart';
import '../../../presentation/widgets/image_picker_bottom_sheet.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_state.dart';
import '../../../statefulbusinesslogic/bloc/user_profile/user_profile_bloc.dart';
import '../../../statefulbusinesslogic/bloc/profile_picture/profile_picture_bloc.dart';
import '../../../statefulbusinesslogic/bloc/profile_picture/profile_picture_event.dart';
import '../../../statefulbusinesslogic/bloc/profile_picture/profile_picture_state.dart';

import '../../../di/injection_container_refactored.dart' as di;
import 'package:image_picker/image_picker.dart';
import '../../widgets/custom_toast.dart';
import 'dart:async';

/// The main user profile screen.
///
/// Displays user information via [UserProfileCard] and provides access to
/// various sub-pages like editing profile, settings (notifications, privacy,
/// security, language), help, and logout functionality.
class ProfilePage extends StatefulWidget {
  /// Constructs a [ProfilePage].
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

/// State for [ProfilePage].
///
/// Manages the visibility of the main navigation bar when navigating
/// to child pages pushed onto the navigation stack.
class _ProfilePageState extends State<ProfilePage> {
  late NavBarVisibilityNotifier _navBarNotifier;
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();
  bool _isUploadingImage = false;
  String? _localImagePath; // Holds local path for immediate preview
  StreamSubscription<ProfilePictureState>? _pictureSub;

  // Cache user data to prevent glitches during profile picture updates
  String? _cachedFirstName;
  String? _cachedLastName;
  String? _cachedUsername;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Obtain the notifier controlling nav bar visibility.
    _navBarNotifier = Provider.of<NavBarVisibilityNotifier>(
      context,
      listen: false,
    );

    // Show nav bar if this page is the current top-most route.
    final route = ModalRoute.of(context);
    if (route?.isCurrent ?? false) {
      _navBarNotifier.showNavBar();
    }
  }

  @override
  void initState() {
    super.initState();
    // Don't clear local image path - let it persist across navigation
    // Load current user profile data
    final authState = context.read<AuthBloc>().state;
    if (authState.status == AuthStatus.authenticated && authState.userId != null) {
      // Force reload to ensure we get the latest profile data (including profile picture)
      // This is especially important after signup when profile picture might have been uploaded
      print('🔍 ProfilePage.initState: Loading user profile for ${authState.userId} with forceReload=true');
      context.read<UserProfileBloc>().add(LoadUserProfileEvent(authState.userId!, forceReload: true));
    }
  }

  @override
  void dispose() {
    _pictureSub?.cancel();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _usernameController.dispose();
    _navBarNotifier.showNavBar();
    super.dispose();
  }

  // void _populateFields(UserModel user) {
  // _firstNameController.text = user.firstName ?? '';
  // _lastNameController.text = user.lastName ?? '';
  // _emailController.text = user.email ?? '';
  // _usernameController.text = user.username ?? '';
  // }

  Future<void> _handleProfilePictureUpdate() async {
    // Show profile picture options immediately - connectivity will be checked during upload
    // This provides instant UI response and better user experience
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) => Container(
          decoration: BoxDecoration(
            color: const Color(0xFF1E1E1E),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 20),
                const Text(
                  'Update Profile Picture',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 20),
                ListTile(
                  leading: const Icon(Icons.camera_alt, color: Colors.white),
                  title: const Text('Camera', style: TextStyle(color: Colors.white)),
                  onTap: () {
                    Navigator.pop(context);
                    _takePhoto();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.photo_library, color: Colors.white),
                  title: const Text('Gallery', style: TextStyle(color: Colors.white)),
                  onTap: () {
                    Navigator.pop(context);
                    _pickFromGallery();
                  },
                ),
                // Show remove option if user has a profile picture
                BlocBuilder<UserProfileBloc, UserProfileState>(
                  builder: (context, profileState) {
                    final hasProfilePicture = profileState is UserProfileLoaded &&
                        profileState.user.profilePictureUrl != null &&
                        profileState.user.profilePictureUrl!.isNotEmpty;

                    if (hasProfilePicture || _localImagePath != null) {
                      return ListTile(
                        leading: const Icon(Icons.delete, color: Colors.red),
                        title: const Text('Remove Picture', style: TextStyle(color: Colors.red)),
                        onTap: () {
                          Navigator.pop(context);
                          _removeProfilePicture();
                        },
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      );
  }

  Future<void> _takePhoto() async {
    if (Navigator.of(context).canPop()) Navigator.of(context).pop();

    final profilePictureBloc = di.sl<ProfilePictureBloc>();
    _handleProfilePictureResult(profilePictureBloc);
    setState(() {
      _isUploadingImage = true;
    });

    // Let the BLoC handle the entire image picking and upload process
    profilePictureBloc.add(const TakePhotoEvent());
  }

  Future<void> _pickFromGallery() async {
    if (Navigator.of(context).canPop()) Navigator.of(context).pop();

    final profilePictureBloc = di.sl<ProfilePictureBloc>();
    _handleProfilePictureResult(profilePictureBloc);
    setState(() {
      _isUploadingImage = true;
    });

    // Let the BLoC handle the entire image picking and upload process
    profilePictureBloc.add(const PickFromGalleryEvent());
  }

  void _handleProfilePictureResult(ProfilePictureBloc profilePictureBloc) {
    _pictureSub?.cancel();
    _pictureSub = profilePictureBloc.stream.listen((state) {
      if (!mounted) return;

      if (state is ProfilePictureSuccess) {
        // Set local path for immediate display, then reload profile from backend
        setState(() {
          _localImagePath = state.result.url;
          _isUploadingImage = false;
        });

        CustomToast.showSuccess(context, state.message ?? 'Profile picture updated successfully!');

        // Single delayed reload to get updated profile from backend
        // This reduces UI glitches by avoiding multiple rapid reloads
        Future.delayed(const Duration(milliseconds: 800), () {
          if (mounted) {
            print('🔄 Reloading user profile to get updated avatar URL from backend');
            final authState = context.read<AuthBloc>().state;
            if (authState.status == AuthStatus.authenticated && authState.userId != null) {
              context.read<UserProfileBloc>().add(LoadUserProfileEvent(authState.userId!, forceReload: true));
            }

            // Clear local path after a short delay to allow backend data to load
            Future.delayed(const Duration(milliseconds: 600), () {
              if (mounted) {
                print('🔄 Clearing local image path to use backend URL');
                setState(() {
                  _localImagePath = null;
                });
              }
            });
          }
        });
      } else if (state is ProfilePictureError) {
        setState(() {
          _isUploadingImage = false;
        });

        // Show enhanced error message with retry option for retryable errors
        if (state.isRetryable) {
          _showRetryableError(state);
        } else {
          CustomToast.showError(context, state.message);
        }
      } else if (state is ProfilePictureCancelled) {
        // User cancelled, no action needed
        setState(() {
          _isUploadingImage = false;
        });
      }
    });
  }

  Future<void> _removeProfilePicture() async {
    // Get current user's profile picture URL
    final authState = context.read<AuthBloc>().state;
    final profileState = context.read<UserProfileBloc>().state;

    if (authState.userId != null && profileState is UserProfileLoaded) {
      final currentProfilePictureUrl = profileState.user.profilePictureUrl;

      if (currentProfilePictureUrl != null && currentProfilePictureUrl.isNotEmpty) {
        // Use the ProfilePictureBloc to remove the profile picture
        final profilePictureBloc = di.sl<ProfilePictureBloc>();
        profilePictureBloc.add(RemoveProfilePictureEvent(imageUrl: currentProfilePictureUrl));

        // Listen for the result
        profilePictureBloc.stream.listen((state) {
          if (!mounted) return;

          if (state is ProfilePictureRemoved) {
            // Reload user profile to reflect the removal
            context.read<UserProfileBloc>().add(LoadUserProfileEvent(authState.userId!));

            setState(() {
              _localImagePath = null;
            });
            CustomToast.showWarning(context, 'Profile picture removed successfully');
          } else if (state is ProfilePictureError) {
            CustomToast.showError(context, 'Failed to remove profile picture: ${state.message}');
          }
        });
      } else {
        CustomToast.showInfo(context, 'No profile picture to remove');
      }
    }
  }

  void _saveProfile() {
    if (_formKey.currentState?.validate() ?? false) {
      // TODO: Implement profile update using proper current user profile service
      // For now, just show success message
      CustomToast.showSuccess(context, 'Profile updated successfully!');
    }
  }

  /// Navigates to a sub-page using [MaterialPageRoute].
  ///
  /// Hides the main navigation bar before pushing the new route and
  /// shows it again upon returning, provided the widget is still mounted.
  ///
  /// [page]: The widget for the sub-page route.
  Future<void> _navigateToSubPage(Widget page) async {
    _navBarNotifier.hideNavBar();
    await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => page),
    );
    if (mounted) {
      _navBarNotifier.showNavBar();
    }
  }

  void _handleImageSelected(String imagePath) {
    // TODO: Update user profile picture using proper current user profile service
    CustomToast.showSuccess(context, 'Profile picture updated!');
  }

  void _showImagePicker() {
    _navBarNotifier.hideNavBar();
    showModalBottomSheet(
      context: context,
      builder: (context) => ImagePickerBottomSheet(
        onImageSelected: _handleImageSelected,
      ),
    ).then((_) => _navBarNotifier.showNavBar());
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final baseTileHeight = screenHeight / 12;
    final iconContainerSize = baseTileHeight * 0.7;
    final imageSize = iconContainerSize * 0.8;

    // Standard trailing icon for navigation list tiles.
    const trailingArrow = Icon(
      Icons.arrow_forward_ios,
      size: 16,
      color: Colors.white70,
    );

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: const MainAppBar(),
        body: SafeArea(
          bottom:
              false, // Prevent overlap with the persistent bottom navigation bar.
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Centered page title.
                const Center(
                  child: Padding(
                    padding: EdgeInsets.zero,
                    child: Text(
                      'Profile',
                      style: TextStyle(
                        color: Color(0xFF00FFFF), // Cyan
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      // Ensure content scrolls above the navigation bar area.
                      padding: const EdgeInsets.only(bottom: 80),
                      child: Column(
                        children: [
                          // --- User Profile Display ---
                          BlocBuilder<UserProfileBloc, UserProfileState>(
                            builder: (context, profileState) {
                              return BlocBuilder<AuthBloc, AuthState>(
                                builder: (context, authState) {
                                  if (authState.status == AuthStatus.authenticated) {
                                    if (profileState is UserProfileLoaded) {
                                      // Use complete user profile data from backend
                                      final user = profileState.user;
                                      final avatarUrl = _localImagePath ?? user.profilePictureUrl;

                                      // Cache user data to prevent glitches during profile reloads
                                      _cachedFirstName = user.firstName ?? '';
                                      _cachedLastName = user.lastName ?? '';
                                      _cachedUsername = user.username ?? '';

                                      print('🖼️ ProfilePage - UserProfileLoaded - user.profilePictureUrl: ${user.profilePictureUrl}');
                                      print('🖼️ ProfilePage - UserProfileLoaded - _localImagePath: $_localImagePath');
                                      print('🖼️ ProfilePage - UserProfileLoaded - final avatarUrl: $avatarUrl');
                                      print('🖼️ ProfilePage - UserProfileLoaded - cached data: $_cachedFirstName $_cachedLastName @$_cachedUsername');

                                      return DynamicUserProfileCard(
                                        firstName: _cachedFirstName!,
                                        lastName: _cachedLastName!,
                                        username: _cachedUsername!,
                                        avatarUrl: avatarUrl,
                                        baseTileHeight: baseTileHeight,
                                        onProfilePictureTap: _isUploadingImage ? null : _handleProfilePictureUpdate,
                                      );
                                    } else if (profileState is UserProfileLoading) {
                                      // Show loading state but preserve cached user data to prevent glitches
                                      print('🖼️ ProfilePage - UserProfileLoading - _localImagePath: $_localImagePath');
                                      print('🖼️ ProfilePage - UserProfileLoading - using cached data: $_cachedFirstName $_cachedLastName @$_cachedUsername');

                                      return DynamicUserProfileCard(
                                        firstName: _cachedFirstName ?? authState.firstName ?? 'Loading...',
                                        lastName: _cachedLastName ?? '',
                                        username: _cachedUsername ?? 'Loading...',
                                        avatarUrl: _localImagePath,
                                        baseTileHeight: baseTileHeight,
                                        onProfilePictureTap: null, // Disable tap during loading
                                      );
                                    } else if (profileState is UserProfileError) {
                                      // Show error state with fallback to cached data, then AuthState data
                                      print('🖼️ ProfilePage - UserProfileError - _localImagePath: $_localImagePath');
                                      print('🖼️ ProfilePage - UserProfileError - using cached data: $_cachedFirstName $_cachedLastName @$_cachedUsername');

                                      return DynamicUserProfileCard(
                                        firstName: _cachedFirstName ?? authState.firstName ?? 'Error loading profile',
                                        lastName: _cachedLastName ?? '',
                                        username: _cachedUsername ?? 'Error',
                                        avatarUrl: _localImagePath,
                                        baseTileHeight: baseTileHeight,
                                        onProfilePictureTap: _isUploadingImage ? null : _handleProfilePictureUpdate,
                                      );
                                    } else {
                                      // Initial state - show loading with cached data or auth data
                                      print('🖼️ ProfilePage - Initial state - _localImagePath: $_localImagePath');
                                      print('🖼️ ProfilePage - Initial state - using cached data: $_cachedFirstName $_cachedLastName @$_cachedUsername');

                                      return DynamicUserProfileCard(
                                        firstName: _cachedFirstName ?? authState.firstName ?? '',
                                        lastName: _cachedLastName ?? '',
                                        username: _cachedUsername ?? 'Loading...',
                                        avatarUrl: _localImagePath,
                                        baseTileHeight: baseTileHeight,
                                        onProfilePictureTap: null,
                                      );
                                    }
                                  }
                                  // Not authenticated - show empty profile
                                  return DynamicUserProfileCard(
                                    firstName: '',
                                    lastName: '',
                                    username: '',
                                    baseTileHeight: baseTileHeight,
                                    onProfilePictureTap: null,
                                  );
                                },
                              );
                            },
                          ),
                          // --- Navigation Options ---
                          ProfileOptionTile(
                            title: 'Edit profile',
                            subtitle:
                                'Update your personal information and photo',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/pencil.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap:
                                () =>
                                    _navigateToSubPage(const EditProfilePage()),
                            trailing: trailingArrow,
                          ),
                          ProfileOptionTile(
                            title: 'Notifications',
                            subtitle:
                                'Manage notifications and security preferences',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/bell.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap:
                                () => _navigateToSubPage(
                                  const NotificationsPage(),
                                ),
                            trailing: trailingArrow,
                          ),
                          ProfileOptionTile(
                            title: 'Privacy',
                            subtitle:
                                'Control your account privacy and blocked users',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/lock.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap:
                                () => _navigateToSubPage(const PrivacyPage()),
                            trailing: trailingArrow,
                          ),
                          ProfileOptionTile(
                            title: 'Language',
                            subtitle: 'Choose your preferred language',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/text.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap:
                                () => _navigateToSubPage(
                                  const LanguageSelectionPage(),
                                ),
                            trailing: trailingArrow,
                          ),
                          ProfileOptionTile(
                            title: 'Security',
                            subtitle:
                                'Manage your password and account security',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/shield.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap:
                                () => _navigateToSubPage(const SecurityPage()),
                            trailing: trailingArrow,
                          ),
                          ProfileOptionTile(
                            title: 'Help & support',
                            subtitle: 'Get help and report issues',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/chat-text.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap:
                                () =>
                                    _navigateToSubPage(const HelpSupportPage()),
                            trailing: trailingArrow,
                          ),
                          ProfileOptionTile(
                            title: 'About',
                            subtitle: 'Learn more about Hopen',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/zoom.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap: () => _navigateToSubPage(const AboutPage()),
                            trailing: trailingArrow,
                          ),
                          // --- Logout Action ---
                          ProfileOptionTile(
                            title: 'Logout',
                            subtitle: 'Sign out of your account',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/moon.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap: () {
                              // Implement actual logout logic
                              context.read<AuthBloc>().add(const LogoutEvent());
                              _navBarNotifier
                                  .showNavBar(); // Ensure nav bar is visible for login screen
                              context.go(
                                AppRoutes.login,
                              ); // Navigate to login via go_router
                            },
                            trailing: trailingArrow,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showRetryableError(ProfilePictureError state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        shape: const RoundedSuperellipseBorder(
          borderRadius: BorderRadius.all(Radius.circular(18)),
        ),
        title: Row(
          children: [
            Icon(
              _getErrorIcon(state.errorType),
              color: _getErrorColor(state.errorType),
            ),
            const SizedBox(width: 8),
            const Text('Upload Failed'),
          ],
        ),
        content: Text(state.message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Retry the last operation - for simplicity, show the bottom sheet again
              _handleProfilePictureUpdate();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  IconData _getErrorIcon(ProfilePictureErrorType errorType) {
    switch (errorType) {
      case ProfilePictureErrorType.network:
        return Icons.wifi_off;
      case ProfilePictureErrorType.storage:
        return Icons.cloud_off;
      case ProfilePictureErrorType.fileSize:
        return Icons.file_present;
      case ProfilePictureErrorType.fileFormat:
        return Icons.image_not_supported;
      default:
        return Icons.error;
    }
  }

  Color _getErrorColor(ProfilePictureErrorType errorType) {
    switch (errorType) {
      case ProfilePictureErrorType.network:
        return Colors.orange;
      case ProfilePictureErrorType.storage:
        return Colors.red;
      case ProfilePictureErrorType.fileSize:
      case ProfilePictureErrorType.fileFormat:
        return Colors.amber;
      default:
        return Colors.red;
    }
  }
}
