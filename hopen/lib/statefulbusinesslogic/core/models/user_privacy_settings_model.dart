import 'package:equatable/equatable.dart';

// Enum to control visibility levels
enum ProfileFieldVisibility { everyone, friendsOnly, contactsOnly, noOne }

// Enum for location sharing preferences
enum LocationSharingLevel { never, friendsOnly, contactsOnly, always }

// Enum for data collection preferences
enum DataCollectionLevel { minimal, standard, full }

class UserPrivacySettingsModel extends Equatable {
  // e.g., everyone, mutualFriends

  const UserPrivacySettingsModel({
    required this.userId,
    this.emailVisibility = ProfileFieldVisibility.friendsOnly,
    this.birthdayVisibility = ProfileFieldVisibility.friendsOnly,
    this.onlineStatusVisibility = ProfileFieldVisibility.contactsOnly,
    this.whoCanSendContactRequests = ProfileFieldVisibility.everyone,
    this.locationSharingLevel = LocationSharingLevel.friendsOnly,
    this.dataCollectionLevel = DataCollectionLevel.standard,
    this.allowAnalytics = true,
    this.allowPersonalizedAds = false,
    this.allowThirdPartySharing = false,
    this.twoFactorAuthEnabled = false,
    this.biometricAuthEnabled = false,
    this.sessionTimeoutMinutes = 30,
  });
  final String userId; // To link to the UserModel

  // Profile visibility settings
  final ProfileFieldVisibility emailVisibility;
  final ProfileFieldVisibility birthdayVisibility;
  final ProfileFieldVisibility onlineStatusVisibility;
  final ProfileFieldVisibility whoCanSendContactRequests;

  // Location and data settings
  final LocationSharingLevel locationSharingLevel;
  final DataCollectionLevel dataCollectionLevel;

  // Privacy preferences
  final bool allowAnalytics;
  final bool allowPersonalizedAds;
  final bool allowThirdPartySharing;

  // Security settings
  final bool twoFactorAuthEnabled;
  final bool biometricAuthEnabled;
  final int sessionTimeoutMinutes;

  UserPrivacySettingsModel copyWith({
    String? userId,
    ProfileFieldVisibility? emailVisibility,
    ProfileFieldVisibility? birthdayVisibility,
    ProfileFieldVisibility? onlineStatusVisibility,
    ProfileFieldVisibility? whoCanSendContactRequests,
    LocationSharingLevel? locationSharingLevel,
    DataCollectionLevel? dataCollectionLevel,
    bool? allowAnalytics,
    bool? allowPersonalizedAds,
    bool? allowThirdPartySharing,
    bool? twoFactorAuthEnabled,
    bool? biometricAuthEnabled,
    int? sessionTimeoutMinutes,
  }) => UserPrivacySettingsModel(
    userId: userId ?? this.userId,
    emailVisibility: emailVisibility ?? this.emailVisibility,
    birthdayVisibility: birthdayVisibility ?? this.birthdayVisibility,
    onlineStatusVisibility: onlineStatusVisibility ?? this.onlineStatusVisibility,
    whoCanSendContactRequests: whoCanSendContactRequests ?? this.whoCanSendContactRequests,
    locationSharingLevel: locationSharingLevel ?? this.locationSharingLevel,
    dataCollectionLevel: dataCollectionLevel ?? this.dataCollectionLevel,
    allowAnalytics: allowAnalytics ?? this.allowAnalytics,
    allowPersonalizedAds: allowPersonalizedAds ?? this.allowPersonalizedAds,
    allowThirdPartySharing: allowThirdPartySharing ?? this.allowThirdPartySharing,
    twoFactorAuthEnabled: twoFactorAuthEnabled ?? this.twoFactorAuthEnabled,
    biometricAuthEnabled: biometricAuthEnabled ?? this.biometricAuthEnabled,
    sessionTimeoutMinutes: sessionTimeoutMinutes ?? this.sessionTimeoutMinutes,
  );

  @override
  List<Object?> get props => [
    userId,
    emailVisibility,
    birthdayVisibility,
    onlineStatusVisibility,
    whoCanSendContactRequests,
    locationSharingLevel,
    dataCollectionLevel,
    allowAnalytics,
    allowPersonalizedAds,
    allowThirdPartySharing,
    twoFactorAuthEnabled,
    biometricAuthEnabled,
    sessionTimeoutMinutes,
  ];

  /// Create UserPrivacySettingsModel from JSON
  factory UserPrivacySettingsModel.fromJson(Map<String, dynamic> json) {
    return UserPrivacySettingsModel(
      userId: json['userId'] as String,
      emailVisibility: ProfileFieldVisibility.values.firstWhere(
        (e) => e.toString() == json['emailVisibility'],
        orElse: () => ProfileFieldVisibility.friendsOnly,
      ),
      birthdayVisibility: ProfileFieldVisibility.values.firstWhere(
        (e) => e.toString() == json['birthdayVisibility'],
        orElse: () => ProfileFieldVisibility.friendsOnly,
      ),
      onlineStatusVisibility: ProfileFieldVisibility.values.firstWhere(
        (e) => e.toString() == json['onlineStatusVisibility'],
        orElse: () => ProfileFieldVisibility.contactsOnly,
      ),
      whoCanSendContactRequests: ProfileFieldVisibility.values.firstWhere(
        (e) => e.toString() == json['whoCanSendContactRequests'],
        orElse: () => ProfileFieldVisibility.everyone,
      ),
      locationSharingLevel: LocationSharingLevel.values.firstWhere(
        (e) => e.toString() == json['locationSharingLevel'],
        orElse: () => LocationSharingLevel.friendsOnly,
      ),
      dataCollectionLevel: DataCollectionLevel.values.firstWhere(
        (e) => e.toString() == json['dataCollectionLevel'],
        orElse: () => DataCollectionLevel.standard,
      ),
      allowAnalytics: json['allowAnalytics'] as bool? ?? true,
      allowPersonalizedAds: json['allowPersonalizedAds'] as bool? ?? false,
      allowThirdPartySharing: json['allowThirdPartySharing'] as bool? ?? false,
      twoFactorAuthEnabled: json['twoFactorAuthEnabled'] as bool? ?? false,
      biometricAuthEnabled: json['biometricAuthEnabled'] as bool? ?? false,
      sessionTimeoutMinutes: json['sessionTimeoutMinutes'] as int? ?? 30,
    );
  }

  /// Convert UserPrivacySettingsModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'emailVisibility': emailVisibility.toString(),
      'birthdayVisibility': birthdayVisibility.toString(),
      'onlineStatusVisibility': onlineStatusVisibility.toString(),
      'whoCanSendContactRequests': whoCanSendContactRequests.toString(),
      'locationSharingLevel': locationSharingLevel.toString(),
      'dataCollectionLevel': dataCollectionLevel.toString(),
      'allowAnalytics': allowAnalytics,
      'allowPersonalizedAds': allowPersonalizedAds,
      'allowThirdPartySharing': allowThirdPartySharing,
      'twoFactorAuthEnabled': twoFactorAuthEnabled,
      'biometricAuthEnabled': biometricAuthEnabled,
      'sessionTimeoutMinutes': sessionTimeoutMinutes,
    };
  }
}
