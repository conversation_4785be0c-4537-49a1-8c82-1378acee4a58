import 'dart:async';
import 'package:flutter/material.dart';
import '../models/user_privacy_settings_model.dart';
import '../models/user_model.dart';
import 'logging_service.dart';

/// Service that manages the user's online status visibility
/// 
/// This service combines:
/// 1. App lifecycle state (foreground/background)
/// 2. User privacy preferences (activity status visibility)
/// 3. User's actual online status from the backend
/// 
/// The user appears online ONLY when:
/// - The app is in the foreground (actively being used)
/// - AND the user has not hidden their activity status
/// - AND the user's backend status is online
class OnlineStatusService with WidgetsBindingObserver {
  static OnlineStatusService? _instance;
  static OnlineStatusService get instance {
    _instance ??= OnlineStatusService._internal();
    return _instance!;
  }

  OnlineStatusService._internal();

  // Stream controllers for status updates
  final StreamController<bool> _visibilityController = StreamController<bool>.broadcast();
  final StreamController<AppLifecycleState> _lifecycleController = StreamController<AppLifecycleState>.broadcast();

  // Current state
  AppLifecycleState _currentLifecycleState = AppLifecycleState.resumed;
  UserPrivacySettingsModel? _privacySettings;
  OnlineStatus _backendOnlineStatus = OnlineStatus.offline;
  bool _isInitialized = false;

  /// Stream of online status visibility changes
  Stream<bool> get visibilityStream => _visibilityController.stream;

  /// Stream of app lifecycle state changes
  Stream<AppLifecycleState> get lifecycleStream => _lifecycleController.stream;

  /// Current computed online status visibility
  bool get isOnlineVisible => _computeOnlineVisibility();

  /// Current app lifecycle state
  AppLifecycleState get currentLifecycleState => _currentLifecycleState;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) {
      LoggingService.info('OnlineStatusService: Already initialized');
      return;
    }

    LoggingService.info('OnlineStatusService: Initializing');

    // Register as app lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    _isInitialized = true;
    LoggingService.info('OnlineStatusService: Initialization complete');

    // Emit initial state
    _emitVisibilityUpdate();
  }

  /// Dispose the service
  void dispose() {
    LoggingService.info('OnlineStatusService: Disposing');
    
    WidgetsBinding.instance.removeObserver(this);
    _visibilityController.close();
    _lifecycleController.close();
    
    _isInitialized = false;
  }

  /// Update privacy settings
  void updatePrivacySettings(UserPrivacySettingsModel? settings) {
    LoggingService.info('OnlineStatusService: Updating privacy settings');
    _privacySettings = settings;
    _emitVisibilityUpdate();
  }

  /// Update backend online status
  void updateBackendOnlineStatus(OnlineStatus status) {
    LoggingService.info('OnlineStatusService: Updating backend online status to $status');
    _backendOnlineStatus = status;
    _emitVisibilityUpdate();
  }

  /// Handle app lifecycle state changes
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    LoggingService.info('OnlineStatusService: App lifecycle state changed to $state');
    
    final previousState = _currentLifecycleState;
    _currentLifecycleState = state;
    
    // Emit lifecycle change
    _lifecycleController.add(state);
    
    // Update visibility based on new lifecycle state
    _emitVisibilityUpdate();

    // Log important transitions
    if (previousState == AppLifecycleState.paused && state == AppLifecycleState.resumed) {
      LoggingService.info('OnlineStatusService: App returned to foreground');
    } else if (previousState == AppLifecycleState.resumed && state == AppLifecycleState.paused) {
      LoggingService.info('OnlineStatusService: App moved to background');
    }
  }

  /// Compute whether the user should appear online
  bool _computeOnlineVisibility() {
    // Must be in foreground
    final isInForeground = _currentLifecycleState == AppLifecycleState.resumed;
    
    // Must have privacy settings allowing visibility
    final allowsVisibility = _privacySettings?.onlineStatusVisibility != ProfileFieldVisibility.noOne;
    
    // Must be online according to backend
    final isBackendOnline = _backendOnlineStatus == OnlineStatus.online;

    final isVisible = isInForeground && (allowsVisibility ?? true) && isBackendOnline;

    LoggingService.debug('OnlineStatusService: Computing visibility - '
        'foreground: $isInForeground, '
        'allows: $allowsVisibility, '
        'backend: $isBackendOnline, '
        'result: $isVisible');

    return isVisible;
  }

  /// Emit visibility update to listeners
  void _emitVisibilityUpdate() {
    final isVisible = _computeOnlineVisibility();
    _visibilityController.add(isVisible);
    
    LoggingService.info('OnlineStatusService: Emitted visibility update - visible: $isVisible');
  }

  /// Get visibility for a specific context (e.g., for other users viewing this user)
  /// This considers the privacy settings and who is viewing
  bool getVisibilityForViewer(String? viewerUserId, List<String> userContactIds, List<String> userFriendIds) {
    if (!isOnlineVisible) return false;
    if (_privacySettings == null) return true; // Default to visible if no settings

    switch (_privacySettings!.onlineStatusVisibility) {
      case ProfileFieldVisibility.everyone:
        return true;
      case ProfileFieldVisibility.friendsOnly:
        return viewerUserId != null && userFriendIds.contains(viewerUserId);
      case ProfileFieldVisibility.contactsOnly:
        return viewerUserId != null && userContactIds.contains(viewerUserId);
      case ProfileFieldVisibility.noOne:
        return false;
    }
  }

  /// Force refresh the online status
  void refresh() {
    LoggingService.info('OnlineStatusService: Force refreshing online status');
    _emitVisibilityUpdate();
  }

  /// Check if the app is currently in foreground
  bool get isAppInForeground => _currentLifecycleState == AppLifecycleState.resumed;

  /// Check if activity status is hidden by user preference
  bool get isActivityStatusHidden => _privacySettings?.onlineStatusVisibility == ProfileFieldVisibility.noOne;

  /// Get current privacy settings
  UserPrivacySettingsModel? get privacySettings => _privacySettings;

  /// Get current backend online status
  OnlineStatus get backendOnlineStatus => _backendOnlineStatus;
}
