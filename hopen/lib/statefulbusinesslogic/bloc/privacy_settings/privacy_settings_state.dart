import 'package:equatable/equatable.dart';
import '../../core/models/user_privacy_settings_model.dart';

abstract class PrivacySettingsState extends Equatable {
  const PrivacySettingsState();

  @override
  List<Object?> get props => [];
}

/// Initial state when privacy settings haven't been loaded yet
class PrivacySettingsInitial extends PrivacySettingsState {
  const PrivacySettingsInitial();
}

/// Loading state when fetching privacy settings
class PrivacySettingsLoading extends PrivacySettingsState {
  const PrivacySettingsLoading();
}

/// Successfully loaded privacy settings
class PrivacySettingsLoaded extends PrivacySettingsState {
  const PrivacySettingsLoaded({
    required this.settings,
  });

  final UserPrivacySettingsModel settings;

  @override
  List<Object?> get props => [settings];
}

/// Error state when loading or updating privacy settings fails
class PrivacySettingsError extends PrivacySettingsState {
  const PrivacySettingsError({
    required this.message,
    this.technicalDetails,
  });

  final String message;
  final String? technicalDetails;

  @override
  List<Object?> get props => [message, technicalDetails];
}

/// State when a privacy setting is being updated
class PrivacySettingsUpdating extends PrivacySettingsState {
  const PrivacySettingsUpdating({
    required this.currentSettings,
    required this.updatingField,
  });

  final UserPrivacySettingsModel currentSettings;
  final String updatingField;

  @override
  List<Object?> get props => [currentSettings, updatingField];
}

/// Successfully updated a privacy setting
class PrivacySettingsUpdated extends PrivacySettingsState {
  const PrivacySettingsUpdated({
    required this.settings,
    required this.updatedField,
    this.message,
  });

  final UserPrivacySettingsModel settings;
  final String updatedField;
  final String? message;

  @override
  List<Object?> get props => [settings, updatedField, message];
}
