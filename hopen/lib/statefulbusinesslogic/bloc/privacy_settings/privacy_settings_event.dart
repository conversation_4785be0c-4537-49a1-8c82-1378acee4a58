import 'package:equatable/equatable.dart';
import '../../core/models/user_privacy_settings_model.dart';

abstract class PrivacySettingsEvent extends Equatable {
  const PrivacySettingsEvent();

  @override
  List<Object?> get props => [];
}

/// Load privacy settings for the current user
class LoadPrivacySettingsEvent extends PrivacySettingsEvent {
  const LoadPrivacySettingsEvent({required this.userId});

  final String userId;

  @override
  List<Object?> get props => [userId];
}

/// Update online status visibility setting
class UpdateOnlineStatusVisibilityEvent extends PrivacySettingsEvent {
  const UpdateOnlineStatusVisibilityEvent({
    required this.visibility,
  });

  final ProfileFieldVisibility visibility;

  @override
  List<Object?> get props => [visibility];
}

/// Update email visibility setting
class UpdateEmailVisibilityEvent extends PrivacySettingsEvent {
  const UpdateEmailVisibilityEvent({
    required this.visibility,
  });

  final ProfileFieldVisibility visibility;

  @override
  List<Object?> get props => [visibility];
}

/// Update birthday visibility setting
class UpdateBirthdayVisibilityEvent extends PrivacySettingsEvent {
  const UpdateBirthdayVisibilityEvent({
    required this.visibility,
  });

  final ProfileFieldVisibility visibility;

  @override
  List<Object?> get props => [visibility];
}

/// Update who can send contact requests setting
class UpdateContactRequestsVisibilityEvent extends PrivacySettingsEvent {
  const UpdateContactRequestsVisibilityEvent({
    required this.visibility,
  });

  final ProfileFieldVisibility visibility;

  @override
  List<Object?> get props => [visibility];
}

/// Update location sharing level
class UpdateLocationSharingEvent extends PrivacySettingsEvent {
  const UpdateLocationSharingEvent({
    required this.level,
  });

  final LocationSharingLevel level;

  @override
  List<Object?> get props => [level];
}

/// Update analytics preference
class UpdateAnalyticsPreferenceEvent extends PrivacySettingsEvent {
  const UpdateAnalyticsPreferenceEvent({
    required this.allowAnalytics,
  });

  final bool allowAnalytics;

  @override
  List<Object?> get props => [allowAnalytics];
}

/// Update personalized ads preference
class UpdatePersonalizedAdsPreferenceEvent extends PrivacySettingsEvent {
  const UpdatePersonalizedAdsPreferenceEvent({
    required this.allowPersonalizedAds,
  });

  final bool allowPersonalizedAds;

  @override
  List<Object?> get props => [allowPersonalizedAds];
}

/// Update third party sharing preference
class UpdateThirdPartySharingPreferenceEvent extends PrivacySettingsEvent {
  const UpdateThirdPartySharingPreferenceEvent({
    required this.allowThirdPartySharing,
  });

  final bool allowThirdPartySharing;

  @override
  List<Object?> get props => [allowThirdPartySharing];
}

/// Reset privacy settings to defaults
class ResetPrivacySettingsEvent extends PrivacySettingsEvent {
  const ResetPrivacySettingsEvent();
}
