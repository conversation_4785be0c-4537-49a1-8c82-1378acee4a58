import 'package:bloc/bloc.dart';
import '../../core/models/user_privacy_settings_model.dart';
import '../../../repositories/local_storage/user_settings_repository.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import 'privacy_settings_event.dart';
import 'privacy_settings_state.dart';

/// BLoC for managing user privacy settings
/// 
/// This BLoC handles:
/// - Loading privacy settings from local storage
/// - Updating individual privacy settings
/// - Persisting changes to local storage
/// - Managing state during updates
class PrivacySettingsBloc extends Bloc<PrivacySettingsEvent, PrivacySettingsState> {
  PrivacySettingsBloc({
    required UserSettingsRepository userSettingsRepository,
  }) : _userSettingsRepository = userSettingsRepository,
       super(const PrivacySettingsInitial()) {
    
    on<LoadPrivacySettingsEvent>(_onLoadPrivacySettings);
    on<UpdateOnlineStatusVisibilityEvent>(_onUpdateOnlineStatusVisibility);
    on<UpdateEmailVisibilityEvent>(_onUpdateEmailVisibility);
    on<UpdateBirthdayVisibilityEvent>(_onUpdateBirthdayVisibility);
    on<UpdateContactRequestsVisibilityEvent>(_onUpdateContactRequestsVisibility);
    on<UpdateLocationSharingEvent>(_onUpdateLocationSharing);
    on<UpdateAnalyticsPreferenceEvent>(_onUpdateAnalyticsPreference);
    on<UpdatePersonalizedAdsPreferenceEvent>(_onUpdatePersonalizedAdsPreference);
    on<UpdateThirdPartySharingPreferenceEvent>(_onUpdateThirdPartySharingPreference);
    on<ResetPrivacySettingsEvent>(_onResetPrivacySettings);
  }

  final UserSettingsRepository _userSettingsRepository;
  static const String _privacySettingsKey = 'user_privacy_settings';

  /// Load privacy settings from local storage
  Future<void> _onLoadPrivacySettings(
    LoadPrivacySettingsEvent event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    emit(const PrivacySettingsLoading());

    try {
      LoggingService.info('PrivacySettingsBloc: Loading privacy settings for user ${event.userId}');
      
      // Try to load from local storage
      final storedSettings = await _userSettingsRepository.getSetting<Map<String, dynamic>>(_privacySettingsKey);
      
      UserPrivacySettingsModel settings;
      if (storedSettings != null) {
        // Parse stored settings
        settings = UserPrivacySettingsModel.fromJson(storedSettings);
        LoggingService.info('PrivacySettingsBloc: Loaded privacy settings from storage');
      } else {
        // Create default settings for the user
        settings = UserPrivacySettingsModel(userId: event.userId);
        LoggingService.info('PrivacySettingsBloc: Created default privacy settings');
        
        // Save default settings to storage
        await _saveSettings(settings);
      }

      emit(PrivacySettingsLoaded(settings: settings));
    } catch (e, stackTrace) {
      LoggingService.error('PrivacySettingsBloc: Error loading privacy settings: $e', stackTrace: stackTrace);
      emit(PrivacySettingsError(
        message: 'Failed to load privacy settings',
        technicalDetails: e.toString(),
      ));
    }
  }

  /// Update online status visibility setting
  Future<void> _onUpdateOnlineStatusVisibility(
    UpdateOnlineStatusVisibilityEvent event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    final currentState = state;
    if (currentState is! PrivacySettingsLoaded) return;

    emit(PrivacySettingsUpdating(
      currentSettings: currentState.settings,
      updatingField: 'onlineStatusVisibility',
    ));

    try {
      final updatedSettings = currentState.settings.copyWith(
        onlineStatusVisibility: event.visibility,
      );

      await _saveSettings(updatedSettings);

      emit(PrivacySettingsUpdated(
        settings: updatedSettings,
        updatedField: 'onlineStatusVisibility',
        message: 'Activity status visibility updated',
      ));

      // Return to loaded state
      emit(PrivacySettingsLoaded(settings: updatedSettings));
    } catch (e, stackTrace) {
      LoggingService.error('PrivacySettingsBloc: Error updating online status visibility: $e', stackTrace: stackTrace);
      emit(PrivacySettingsError(
        message: 'Failed to update activity status visibility',
        technicalDetails: e.toString(),
      ));
      // Return to previous state
      emit(PrivacySettingsLoaded(settings: currentState.settings));
    }
  }

  /// Update email visibility setting
  Future<void> _onUpdateEmailVisibility(
    UpdateEmailVisibilityEvent event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    await _updateVisibilitySetting(
      emit,
      'emailVisibility',
      (settings) => settings.copyWith(emailVisibility: event.visibility),
      'Email visibility updated',
    );
  }

  /// Update birthday visibility setting
  Future<void> _onUpdateBirthdayVisibility(
    UpdateBirthdayVisibilityEvent event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    await _updateVisibilitySetting(
      emit,
      'birthdayVisibility',
      (settings) => settings.copyWith(birthdayVisibility: event.visibility),
      'Birthday visibility updated',
    );
  }

  /// Update contact requests visibility setting
  Future<void> _onUpdateContactRequestsVisibility(
    UpdateContactRequestsVisibilityEvent event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    await _updateVisibilitySetting(
      emit,
      'contactRequestsVisibility',
      (settings) => settings.copyWith(whoCanSendContactRequests: event.visibility),
      'Contact requests visibility updated',
    );
  }

  /// Update location sharing setting
  Future<void> _onUpdateLocationSharing(
    UpdateLocationSharingEvent event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    await _updateVisibilitySetting(
      emit,
      'locationSharing',
      (settings) => settings.copyWith(locationSharingLevel: event.level),
      'Location sharing updated',
    );
  }

  /// Update analytics preference
  Future<void> _onUpdateAnalyticsPreference(
    UpdateAnalyticsPreferenceEvent event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    await _updateVisibilitySetting(
      emit,
      'analytics',
      (settings) => settings.copyWith(allowAnalytics: event.allowAnalytics),
      'Analytics preference updated',
    );
  }

  /// Update personalized ads preference
  Future<void> _onUpdatePersonalizedAdsPreference(
    UpdatePersonalizedAdsPreferenceEvent event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    await _updateVisibilitySetting(
      emit,
      'personalizedAds',
      (settings) => settings.copyWith(allowPersonalizedAds: event.allowPersonalizedAds),
      'Personalized ads preference updated',
    );
  }

  /// Update third party sharing preference
  Future<void> _onUpdateThirdPartySharingPreference(
    UpdateThirdPartySharingPreferenceEvent event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    await _updateVisibilitySetting(
      emit,
      'thirdPartySharing',
      (settings) => settings.copyWith(allowThirdPartySharing: event.allowThirdPartySharing),
      'Third party sharing preference updated',
    );
  }

  /// Reset privacy settings to defaults
  Future<void> _onResetPrivacySettings(
    ResetPrivacySettingsEvent event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    final currentState = state;
    if (currentState is! PrivacySettingsLoaded) return;

    emit(PrivacySettingsUpdating(
      currentSettings: currentState.settings,
      updatingField: 'reset',
    ));

    try {
      final defaultSettings = UserPrivacySettingsModel(userId: currentState.settings.userId);
      await _saveSettings(defaultSettings);

      emit(PrivacySettingsUpdated(
        settings: defaultSettings,
        updatedField: 'reset',
        message: 'Privacy settings reset to defaults',
      ));

      // Return to loaded state
      emit(PrivacySettingsLoaded(settings: defaultSettings));
    } catch (e, stackTrace) {
      LoggingService.error('PrivacySettingsBloc: Error resetting privacy settings: $e', stackTrace: stackTrace);
      emit(PrivacySettingsError(
        message: 'Failed to reset privacy settings',
        technicalDetails: e.toString(),
      ));
      // Return to previous state
      emit(PrivacySettingsLoaded(settings: currentState.settings));
    }
  }

  /// Helper method to update visibility settings
  Future<void> _updateVisibilitySetting(
    Emitter<PrivacySettingsState> emit,
    String fieldName,
    UserPrivacySettingsModel Function(UserPrivacySettingsModel) updateFunction,
    String successMessage,
  ) async {
    final currentState = state;
    if (currentState is! PrivacySettingsLoaded) return;

    emit(PrivacySettingsUpdating(
      currentSettings: currentState.settings,
      updatingField: fieldName,
    ));

    try {
      final updatedSettings = updateFunction(currentState.settings);
      await _saveSettings(updatedSettings);

      emit(PrivacySettingsUpdated(
        settings: updatedSettings,
        updatedField: fieldName,
        message: successMessage,
      ));

      // Return to loaded state
      emit(PrivacySettingsLoaded(settings: updatedSettings));
    } catch (e, stackTrace) {
      LoggingService.error('PrivacySettingsBloc: Error updating $fieldName: $e', stackTrace: stackTrace);
      emit(PrivacySettingsError(
        message: 'Failed to update $fieldName',
        technicalDetails: e.toString(),
      ));
      // Return to previous state
      emit(PrivacySettingsLoaded(settings: currentState.settings));
    }
  }

  /// Save settings to local storage
  Future<void> _saveSettings(UserPrivacySettingsModel settings) async {
    await _userSettingsRepository.saveSetting(_privacySettingsKey, settings.toJson());
    LoggingService.info('PrivacySettingsBloc: Privacy settings saved to storage');
  }
}
